"use client"

import { BaseEdge, type EdgeProps, getSmoothStepPath } from "@xyflow/react"

export const ShoshinEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  selected = false,
  animated = true,
}: EdgeProps) => {
  const isHorizontal = sourcePosition === 'right' || sourcePosition === 'left'

  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
    offset: isHorizontal ? 30 : 20,
  })

  const markerId = `shoshin-arrow-${id}`

  // Enhanced styling with selection and animation states
  const edgeStyle = {
    strokeWidth: selected ? 3 : 2,
    stroke: selected ? '#9ca3af' : '#d1d5db',
    strokeDasharray: animated ? '5,5' : 'none',
    transition: 'all 0.2s ease-in-out',
    ...style,
  }

  return (
    <g>
      <defs>
        <marker
          id={markerId}
          viewBox="0 0 12 12"
          refX="11"
          refY="6"
          markerWidth="8"
          markerHeight="8"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <path
            d="M2,2 L10,6 L2,10 L4,6 z"
            fill={selected ? '#9ca3af' : '#d1d5db'}
            stroke={selected ? '#9ca3af' : '#d1d5db'}
            strokeWidth="1"
          />
        </marker>
      </defs>
      <BaseEdge
        path={edgePath}
        style={edgeStyle}
        markerEnd={`url(#${markerId})`}
        interactionWidth={20}
      />
      {animated && (
        <animate
          attributeName="stroke-dashoffset"
          from="10"
          to="0"
          dur="1s"
          repeatCount="indefinite"
        />
      )}
    </g>
  )
}
