"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>abel<PERSON><PERSON><PERSON>, type EdgeP<PERSON>, getSmoothStepPath } from "@xyflow/react"
import { X } from "lucide-react"
import { useEditorStore } from "../../stores/editorStore"

export const ShoshinEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  selected = false,
  animated = true,
}: EdgeProps) => {
  const { deleteSelected } = useEditorStore()
  const isHorizontal = sourcePosition === 'right' || sourcePosition === 'left'

  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
    offset: isHorizontal ? 30 : 20,
  })

  const markerId = `shoshin-arrow-${id}`

  // Enhanced styling with selection and animation states
  const edgeStyle = {
    strokeWidth: selected ? 3 : 2,
    stroke: selected ? '#6b7280' : '#9ca3af',
    strokeDasharray: animated ? '5,5' : 'none',
    transition: 'all 0.2s ease-in-out',
    ...style,
  }

  // Calculate the midpoint of the edge for positioning the delete button
  const edgeMidX = (sourceX + targetX) / 2
  const edgeMidY = (sourceY + targetY) / 2

  const handleDelete = (event: React.MouseEvent) => {
    event.stopPropagation()
    deleteSelected()
  }

  return (
    <>
      <g>
        <defs>
          <marker
            id={markerId}
            viewBox="0 0 12 12"
            refX="11"
            refY="6"
            markerWidth="8"
            markerHeight="8"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <path
              d="M2,2 L10,6 L2,10 L4,6 z"
              fill={selected ? '#6b7280' : '#9ca3af'}
              stroke={selected ? '#6b7280' : '#9ca3af'}
              strokeWidth="1"
            />
          </marker>
        </defs>
        <BaseEdge
          path={edgePath}
          style={edgeStyle}
          markerEnd={`url(#${markerId})`}
          interactionWidth={20}
        />
        {animated && (
          <animate
            attributeName="stroke-dashoffset"
            from="10"
            to="0"
            dur="1s"
            repeatCount="indefinite"
          />
        )}
      </g>

      {/* Delete button when edge is selected */}
      {selected && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${edgeMidX}px, ${edgeMidY}px)`,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <button
              onClick={handleDelete}
              className="flex items-center justify-center w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg transition-colors duration-200 border border-red-600"
              title="Delete edge"
            >
              <X size={12} />
            </button>
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
}
